import AppMail from '#mails/app_mail'
import ZnSalonConstructionServiceSignup from '#models/zn_salon_construction_service_signup'
import env from '#start/env'

export default class SalonConstructionApprovedNotificationSimple extends AppMail {
  subject = 'Great News! Your Salon Construction Service Application Has Been Approved 🎉'

  constructor(private signup: ZnSalonConstructionServiceSignup) {
    super()
  }

  /**
   * The "prepare" method is called automatically when
   * the email is sent or queued.
   */
  prepare() {
    super.prepare()

    const supportEmail = env.get('SUPPORT_EMAIL') || ''
    const supportPhone = env.get('SUPPORT_PHONE_NUMBER') || ''

    this.message
      .html(
        `
        <html>
          <body style="font-family: 'Inter', sans-serif; margin: 0; padding: 0; background: #f1f1f1;">
            <div style="width: 950px; max-width: 100%; background: #f1f1f1; padding: 8px 24px; margin: 0 auto;">
              <div style="text-align: center; margin-bottom: 10px;">
                <img src="${this.baseUrl}/images/logo.png" style="width: 42px; height: 40px;" />
              </div>
              <div style="background: #ffffff; border-top: 4px solid #1d1c20; padding: 24px;">
                <h1 style="font-size: 24px; font-weight: 600; color: #1d1c20; margin: 0 0 16px 0;">
                  Great News! Your Salon Construction Service Application Has Been Approved 🎉
                </h1>
                
                <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
                  Dear ${this.signup.fullName},
                </p>
                
                <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
                  Congratulations! We're excited to inform you that your salon construction service application has been approved. 
                  We're thrilled to help bring your vision to life!
                </p>
                
                <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
                  <b>Your Application Details:</b>
                </p>
                
                <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 16px 0;">
                  <p style="margin: 0 0 8px 0; font-size: 14px; color: #1d1c20;">
                    <strong>Business Name:</strong> ${this.signup.businessName}
                  </p>
                  <p style="margin: 0 0 8px 0; font-size: 14px; color: #1d1c20;">
                    <strong>Contact Email:</strong> ${this.signup.emailAddress}
                  </p>
                  <p style="margin: 0 0 8px 0; font-size: 14px; color: #1d1c20;">
                    <strong>Phone Number:</strong> ${this.signup.phoneNumber}
                  </p>
                </div>
                
                <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
                  <b>What happens next?</b>
                </p>
                
                <div style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
                  <ol>
                    <li>Our construction team will contact you within 2-3 business days to schedule an initial consultation</li>
                    <li>We'll conduct a detailed site assessment and discuss your specific requirements</li>
                    <li>You'll receive a comprehensive project proposal with timeline and pricing</li>
                    <li>Once approved, we'll begin transforming your salon space!</li>
                  </ol>
                </div>
                
                <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
                  We're committed to delivering exceptional results and creating a beautiful, functional space that will help your 
                  business thrive. Our experienced team will work closely with you throughout the entire process.
                </p>
                
                <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
                  If you have any questions or need immediate assistance, please don't hesitate to reach out to our support team.
                </p>
                
                <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
                  Best regards,<br>
                  The Zurno Construction Team
                </p>
              </div>
              
              <div style="background: #ffffff; padding: 24px; color: #4f4d55; font-size: 12px; border-top: 1px solid #f1f1f1;">
                <p>
                  Need help? Contact us at
                  <a target="_blank" style="color: #1D1C20; text-decoration: underline;" href="mailto:${supportEmail}">${supportEmail}</a>
                  or call ${supportPhone}.
                </p>
                <p>
                  Copyright © 2025 Zurno. All rights reserved.
                </p>
              </div>
            </div>
          </body>
        </html>
      `
      )
      .to(this.signup.emailAddress)
  }
}
