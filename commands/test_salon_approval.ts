import { BaseCommand } from '@adonisjs/core/ace'
import type { CommandOptions } from '@adonisjs/core/types/ace'
import ZnSalonConstructionServiceSignup from '#models/zn_salon_construction_service_signup'
import { ESalonConstructionServiceStatus } from '../app/constants/salon_construction_service.js'
import { EmailService } from '../app/services/email_service.js'
import SalonConstructionApprovedNotification from '#mails/salon_construction_service/salon_construction_approved_notification'
import SalonConstructionRejectedNotification from '#mails/salon_construction_service/salon_construction_rejected_notification'

export default class TestSalonApproval extends BaseCommand {
  static commandName = 'test:salon-approval'
  static description = 'Test salon construction service approval/rejection email flow'

  static options: CommandOptions = {
    startApp: true,
  }

  async run() {
    try {
      this.logger.info('Testing salon construction service approval flow...')
      
      // Get the existing signup
      const signup = await ZnSalonConstructionServiceSignup.query().first()
      
      if (!signup) {
        this.logger.error('No salon construction service signup found')
        return
      }
      
      this.logger.info(`Testing with signup: ${signup.fullName} (${signup.emailAddress})`)
      
      // Test approval email
      this.logger.info('Testing approval email...')
      try {
        const emailService = new EmailService()
        const approvedNotification = new SalonConstructionApprovedNotification(signup)
        
        // Try direct send first
        this.logger.info('Attempting direct send...')
        await emailService.send(approvedNotification)
        this.logger.info('✓ Approval email sent successfully via direct send')
        
      } catch (error) {
        this.logger.error('✗ Direct send failed:', error.message)
        
        // Try queued send
        this.logger.info('Attempting queued send...')
        try {
          const emailService = new EmailService()
          const approvedNotification = new SalonConstructionApprovedNotification(signup)
          await emailService.sendLater(approvedNotification)
          this.logger.info('✓ Approval email queued successfully')
        } catch (queueError) {
          this.logger.error('✗ Queued send also failed:', queueError.message)
        }
      }
      
      // Test rejection email
      this.logger.info('Testing rejection email...')
      try {
        const emailService = new EmailService()
        const rejectedNotification = new SalonConstructionRejectedNotification(signup, 'Test rejection reason')
        
        // Try direct send first
        this.logger.info('Attempting direct send...')
        await emailService.send(rejectedNotification)
        this.logger.info('✓ Rejection email sent successfully via direct send')
        
      } catch (error) {
        this.logger.error('✗ Direct send failed:', error.message)
        
        // Try queued send
        this.logger.info('Attempting queued send...')
        try {
          const emailService = new EmailService()
          const rejectedNotification = new SalonConstructionRejectedNotification(signup, 'Test rejection reason')
          await emailService.sendLater(rejectedNotification)
          this.logger.info('✓ Rejection email queued successfully')
        } catch (queueError) {
          this.logger.error('✗ Queued send also failed:', queueError.message)
        }
      }
      
      this.logger.info('Test completed')
      
    } catch (error) {
      this.logger.error('Test failed:', error.message)
    }
  }
}
