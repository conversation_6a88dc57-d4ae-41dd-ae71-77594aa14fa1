import { BaseCommand } from '@adonisjs/core/ace'
import type { CommandOptions } from '@adonisjs/core/types/ace'
import mail from '@adonisjs/mail/services/main'
import SalonConstructionApprovedNotification from '#mails/salon_construction_service/salon_construction_approved_notification'
import SalonConstructionRejectedNotification from '#mails/salon_construction_service/salon_construction_rejected_notification'
import ZnSalonConstructionServiceSignup from '#models/zn_salon_construction_service_signup'
import EmailLog from '#models/zn_email_log'
import env from '#start/env'

export default class TestEmail extends BaseCommand {
  static commandName = 'test:email'
  static description = 'Test email functionality for salon construction service notifications'

  static options: CommandOptions = {
    startApp: true,
  }

  async run() {
    try {
      this.logger.info('Testing email configuration...')

      // Check environment variables
      const requiredEnvVars = [
        'SMTP_HOST',
        'SMTP_PORT',
        'SMTP_USERNAME',
        'SMTP_PASSWORD',
        'MAIL_FROM_ADDRESS',
        'MAIL_FROM_NAME',
        'SUPPORT_EMAIL',
      ]

      const missingVars = requiredEnvVars.filter((varName) => !env.get(varName))

      if (missingVars.length > 0) {
        this.logger.error(`Missing environment variables: ${missingVars.join(', ')}`)
        return
      }

      this.logger.info('✓ All required environment variables are set')

      // Get a test signup record
      const testSignup = await ZnSalonConstructionServiceSignup.query().first()

      if (!testSignup) {
        this.logger.error('No salon construction service signup found for testing')
        return
      }

      this.logger.info(`Found test signup: ${testSignup.fullName} (${testSignup.emailAddress})`)

      // Test simple mail first
      this.logger.info('Testing simple mail...')
      try {
        await mail.send((message) => {
          message
            .to(env.get('SUPPORT_EMAIL'))
            .from(env.get('MAIL_FROM_ADDRESS'), env.get('MAIL_FROM_NAME'))
            .subject('Test Email from Salon Construction Service')
            .html('<p>This is a test email to verify SMTP configuration.</p>')
        })
        this.logger.info('✓ Simple mail sent successfully')
      } catch (error) {
        this.logger.error('✗ Failed to send simple mail:', error.message)
        this.logger.error('Full error:', error)
        return // Exit early if basic mail fails
      }

      // Test approved notification
      this.logger.info('Testing approved notification...')
      try {
        const approvedNotification = new SalonConstructionApprovedNotification(testSignup)
        this.logger.info('Created notification instance')

        // Try to prepare the message to see if template rendering works
        approvedNotification.prepare()
        this.logger.info('Template preparation successful')

        await mail.send(approvedNotification)
        this.logger.info('✓ Approved notification sent successfully')
      } catch (error) {
        this.logger.error('✗ Failed to send approved notification:', error.message)
        this.logger.error('Error stack:', error.stack)
        if (error.code) {
          this.logger.error('Error code:', error.code)
        }
      }

      // Test rejected notification
      this.logger.info('Testing rejected notification...')
      try {
        const rejectedNotification = new SalonConstructionRejectedNotification(
          testSignup,
          'Test rejection reason'
        )
        await mail.send(rejectedNotification)
        this.logger.info('✓ Rejected notification sent successfully')
      } catch (error) {
        this.logger.error('✗ Failed to send rejected notification:', error.message)
        this.logger.error('Full error:', error)
      }

      // Test queue functionality
      this.logger.info('Testing queue functionality...')
      try {
        const queuedNotification = new SalonConstructionApprovedNotification(testSignup)
        await mail.sendLater(queuedNotification)
        this.logger.info('✓ Queued notification dispatched successfully')
      } catch (error) {
        this.logger.error('✗ Failed to queue notification:', error.message)
      }

      // Check recent email logs
      this.logger.info('Checking recent email logs...')
      try {
        const recentLogs = await EmailLog.query().orderBy('createdAt', 'desc').limit(5)

        if (recentLogs.length > 0) {
          this.logger.info('Recent email logs:')
          recentLogs.forEach((log, index) => {
            this.logger.info(
              `${index + 1}. To: ${log.to}, Subject: ${log.subject}, Status: ${log.status}`
            )
            if (log.error) {
              this.logger.error(`   Error: ${log.error}`)
            }
          })
        } else {
          this.logger.info('No recent email logs found')
        }
      } catch (error) {
        this.logger.error('Failed to fetch email logs:', error.message)
      }

      this.logger.info('Email test completed')
    } catch (error) {
      this.logger.error('Email test failed:', error.message)
    }
  }
}
