import { BaseCommand } from '@adonisjs/core/ace'
import type { CommandOptions } from '@adonisjs/core/types/ace'
import ZnSalonConstructionServiceSignup from '#models/zn_salon_construction_service_signup'
import SalonConstructionApprovedNotification from '#mails/salon_construction_service/salon_construction_approved_notification'
import SalonConstructionApprovedNotificationSimple from '#mails/salon_construction_service/salon_construction_approved_notification_simple'
import env from '#start/env'

export default class DebugSalonEmail extends BaseCommand {
  static commandName = 'debug:salon-email'
  static description = 'Debug salon construction service email template rendering'

  static options: CommandOptions = {
    startApp: true,
  }

  async run() {
    try {
      this.logger.info('Debugging salon construction service email...')

      // Get the existing signup
      const signup = await ZnSalonConstructionServiceSignup.query().first()

      if (!signup) {
        this.logger.error('No salon construction service signup found')
        return
      }

      this.logger.info(`Testing with signup: ${signup.fullName} (${signup.emailAddress})`)

      // Create notification instance
      this.logger.info('Creating notification instance...')
      const notification = new SalonConstructionApprovedNotification(signup)

      // Check baseUrl
      this.logger.info(`Base URL: ${notification.baseUrl}`)

      // Check signup data before prepare
      this.logger.info('Signup data before prepare:')
      this.logger.info(`- emailAddress: "${signup.emailAddress}"`)
      this.logger.info(`- fullName: "${signup.fullName}"`)

      // Try to call prepare method
      this.logger.info('Calling prepare method...')
      try {
        notification.prepare()
        this.logger.info('✓ Prepare method completed successfully')

        // Check if message has recipient
        const message = notification.message
        this.logger.info(`Message object exists: ${!!message}`)

        // Try to access message properties
        try {
          const messageData = message.toJSON()
          this.logger.info('Message data:', JSON.stringify(messageData, null, 2))

          // Check specifically for recipients
          if (messageData.to) {
            this.logger.info(`Recipients found: ${JSON.stringify(messageData.to)}`)
          } else {
            this.logger.error('No recipients found in message data')
          }
        } catch (error) {
          this.logger.error('Failed to get message data:', error.message)
        }
      } catch (error) {
        this.logger.error('✗ Prepare method failed:', error.message)
        this.logger.error('Error stack:', error.stack)
      }

      // Test environment variables used in template
      this.logger.info('Environment variables:')
      this.logger.info(`SUPPORT_EMAIL: ${env.get('SUPPORT_EMAIL')}`)
      this.logger.info(`SUPPORT_PHONE_NUMBER: ${env.get('SUPPORT_PHONE_NUMBER')}`)
      this.logger.info(`BASE_URL: ${env.get('BASE_URL')}`)

      // Test simple version without Edge template
      this.logger.info('Testing simple version without Edge template...')
      try {
        const simpleNotification = new SalonConstructionApprovedNotificationSimple(signup)
        simpleNotification.prepare()

        const simpleMessage = simpleNotification.message
        const simpleMessageData = simpleMessage.toJSON()

        if (simpleMessageData.to) {
          this.logger.info(
            `✓ Simple version has recipients: ${JSON.stringify(simpleMessageData.to)}`
          )
        } else {
          this.logger.error('✗ Simple version also has no recipients')
        }
      } catch (error) {
        this.logger.error('Simple version failed:', error.message)
      }

      // Test step-by-step message building
      this.logger.info('Testing step-by-step message building...')
      try {
        const stepNotification = new SalonConstructionApprovedNotificationSimple(signup)

        this.logger.info('Before prepare - message exists:', !!stepNotification.message)

        // Call prepare
        stepNotification.prepare()

        this.logger.info('After prepare - message exists:', !!stepNotification.message)

        // Try to manually set recipient to test
        this.logger.info('Manually setting recipient...')
        stepNotification.message.to('<EMAIL>')

        const testMessageData = stepNotification.message.toJSON()
        this.logger.info('After manual .to() call:', JSON.stringify(testMessageData, null, 2))
      } catch (error) {
        this.logger.error('Step-by-step test failed:', error.message)
        this.logger.error('Error stack:', error.stack)
      }

      this.logger.info('Debug completed')
    } catch (error) {
      this.logger.error('Debug failed:', error.message)
      this.logger.error('Error stack:', error.stack)
    }
  }
}
