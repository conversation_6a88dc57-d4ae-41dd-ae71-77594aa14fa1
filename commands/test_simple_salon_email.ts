import { BaseCommand } from '@adonisjs/core/ace'
import type { CommandOptions } from '@adonisjs/core/types/ace'
import mail from '@adonisjs/mail/services/main'
import ZnSalonConstructionServiceSignup from '#models/zn_salon_construction_service_signup'
import env from '#start/env'

export default class TestSimpleSalonEmail extends BaseCommand {
  static commandName = 'test:simple-salon-email'
  static description = 'Test salon construction service email with simple HTML'

  static options: CommandOptions = {
    startApp: true,
  }

  async run() {
    try {
      this.logger.info('Testing simple salon construction service email...')
      
      // Get the existing signup
      const signup = await ZnSalonConstructionServiceSignup.query().first()
      
      if (!signup) {
        this.logger.error('No salon construction service signup found')
        return
      }
      
      this.logger.info(`Testing with signup: ${signup.fullName} (${signup.emailAddress})`)
      
      // Test simple HTML email
      this.logger.info('Testing simple HTML email...')
      try {
        await mail.send((message) => {
          message
            .to(signup.emailAddress)
            .from(env.get('MAIL_FROM_ADDRESS'), env.get('MAIL_FROM_NAME'))
            .subject('Test: Your Salon Construction Service Application Has Been Approved')
            .html(`
              <html>
                <body>
                  <h1>Great News! Your Salon Construction Service Application Has Been Approved 🎉</h1>
                  <p>Dear ${signup.fullName},</p>
                  <p>Congratulations! We're excited to inform you that your salon construction service application has been approved.</p>
                  <p><strong>Your Application Details:</strong></p>
                  <ul>
                    <li>Business Name: ${signup.businessName}</li>
                    <li>Email: ${signup.emailAddress}</li>
                    <li>Phone: ${signup.phoneNumber}</li>
                  </ul>
                  <p>Best regards,<br>The Zurno Team</p>
                </body>
              </html>
            `)
        })
        this.logger.info('✓ Simple HTML email sent successfully')
        
      } catch (error) {
        this.logger.error('✗ Simple HTML email failed:', error.message)
        this.logger.error('Full error:', error)
      }
      
      // Test queued simple email
      this.logger.info('Testing queued simple email...')
      try {
        await mail.sendLater((message) => {
          message
            .to(signup.emailAddress)
            .from(env.get('MAIL_FROM_ADDRESS'), env.get('MAIL_FROM_NAME'))
            .subject('Test: Your Salon Construction Service Application Has Been Rejected')
            .html(`
              <html>
                <body>
                  <h1>Update on Your Salon Construction Service Application</h1>
                  <p>Dear ${signup.fullName},</p>
                  <p>Thank you for your interest in our salon construction services. After careful review of your application, we regret to inform you that we are unable to proceed with your project at this time.</p>
                  <p><strong>Reason:</strong> Test rejection reason</p>
                  <p>Best regards,<br>The Zurno Team</p>
                </body>
              </html>
            `)
        })
        this.logger.info('✓ Queued simple email dispatched successfully')
        
      } catch (error) {
        this.logger.error('✗ Queued simple email failed:', error.message)
        this.logger.error('Full error:', error)
      }
      
      this.logger.info('Test completed')
      
    } catch (error) {
      this.logger.error('Test failed:', error.message)
    }
  }
}
