import type { HttpContext } from '@adonisjs/core/http'
import ZnSalonConstructionServiceSignup from '#models/zn_salon_construction_service_signup'
import {
  updateSalonConstructionServiceStatusValidator,
  salonConstructionServiceFilterValidator,
} from '#validators/salon_construction_service/salon_construction_service_validator'
import {
  SALON_CONSTRUCTION_SERVICE_TEXT,
  ESalonConstructionServiceStatus,
} from '../../../app/constants/salon_construction_service.js'
import { ACTION, RESOURCE } from '../../../app/constants/authorization.js'
import mail from '@adonisjs/mail/services/main'
import env from '#start/env'
import { DateTimeService } from '../../../services/datetime_service.js'

export default class AdminSalonConstructionServiceController {
  /**
   * @index
   * @tag Admin Salon Construction Service
   * @summary Get all salon construction service signups with filtering
   * @paramQuery page - Page number - @type(number) @example(1)
   * @paramQuery limit - Items per page - @type(number) @example(10)
   * @paramQuery status - Filter by status - @type(string) @example(pending)
   * @paramQuery search - Search by name, email, or business name - @type(string)
   * @paramQuery startDate - Filter from date - @type(date)
   * @paramQuery endDate - Filter to date - @type(date)
   * @paramQuery budgetRange - Filter by budget range - @type(string)
   * @responseBody 200 - {"success":true,"data":{"data":[<ZnSalonConstructionServiceSignup>],"meta":{}}} - Success
   */
  async index({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.SALON_CONSTRUCTION_SERVICE)

    try {
      const filters = await salonConstructionServiceFilterValidator.validate(request.qs())

      const page = filters.page || 1
      const limit = filters.limit || 10

      const query = ZnSalonConstructionServiceSignup.query()
        .preload('pdfFile')
        .orderBy('createdAt', 'desc')

      if (filters.status) {
        query.where('status', filters.status)
      }

      if (filters.search) {
        query.where((builder) => {
          builder
            .whereILike('fullName', `%${filters.search}%`)
            .orWhereILike('businessName', `%${filters.search}%`)
            .orWhereILike('emailAddress', `%${filters.search}%`)
        })
      }

      if (filters.startDate) {
        const startDateStr =
          typeof filters.startDate === 'string'
            ? filters.startDate
            : filters.startDate.toISOString()
        const startISO = DateTimeService.fromISOUS(startDateStr).toISO() || ''
        query.where('createdAt', '>=', startISO)
      }

      if (filters.endDate) {
        const endDateStr =
          typeof filters.endDate === 'string' ? filters.endDate : filters.endDate.toISOString()
        const endISO = DateTimeService.fromISOUS(endDateStr).toISO() || ''
        query.where('createdAt', '<=', endISO)
      }

      if (filters.budgetRange) {
        query.where('budgetRange', filters.budgetRange)
      }

      const signups = await query.paginate(page, limit)

      return response.ok({
        success: true,
        data: signups,
      })
    } catch (error) {
      console.error('Error fetching salon construction service signups:', error)
      return response.internalServerError({
        success: false,
        message: 'Failed to fetch signups',
      })
    }
  }

  /**
   * @show
   * @tag Admin Salon Construction Service
   * @summary Get salon construction service signup by ID
   * @paramPath id - Signup ID - @type(string) @required
   * @responseBody 200 - {"success":true,"data":<ZnSalonConstructionServiceSignup>} - Success
   * @responseBody 404 - {"success":false,"message":"Salon construction service signup not found"} - Not Found
   */
  async show({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.SALON_CONSTRUCTION_SERVICE)

    try {
      const signup = await ZnSalonConstructionServiceSignup.query()
        .where('id', params.id)
        .preload('pdfFile')
        .first()

      if (!signup) {
        return response.notFound({
          success: false,
          message: SALON_CONSTRUCTION_SERVICE_TEXT.NOT_FOUND,
        })
      }

      return response.ok({
        success: true,
        data: signup,
      })
    } catch (error) {
      console.error('Error fetching salon construction service signup:', error)
      return response.internalServerError({
        success: false,
        message: 'Failed to fetch signup',
      })
    }
  }

  /**
   * @update
   * @tag Admin Salon Construction Service
   * @summary Update salon construction service signup status (approve/reject)
   * @paramPath id - Signup ID - @type(string) @required
   * @requestBody {"status":"approved|rejected","rejectionReason":"Optional reason for rejection"}
   * @responseBody 200 - {"success":true,"message":"Status updated successfully","data":<ZnSalonConstructionServiceSignup>} - Success
   * @responseBody 404 - {"success":false,"message":"Salon construction service signup not found"} - Not Found
   * @responseBody 400 - {"success":false,"message":"Validation failed"} - Validation Error
   */
  async update({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.SALON_CONSTRUCTION_SERVICE)

    try {
      const data = request.all()
      const payload = await updateSalonConstructionServiceStatusValidator.validate(data)

      const signup = await ZnSalonConstructionServiceSignup.findOrFail(params.id)

      signup.status = payload.status

      if (payload.status === ESalonConstructionServiceStatus.REJECTED && payload.rejectionReason) {
        signup.additionalNotes =
          `${signup.additionalNotes || ''}\n\nRejection Reason: ${payload.rejectionReason}`.trim()
      }

      await signup.save()

      try {
        if (payload.status === ESalonConstructionServiceStatus.APPROVED) {
          console.log('Sending approved email')

          // Use direct mail.sendLater with exact template structure
          await mail.sendLater((message) => {
            const supportEmail = env.get('SUPPORT_EMAIL') || ''
            const supportPhone = env.get('SUPPORT_PHONE_NUMBER') || ''
            const serverDomain = env.get('BASE_URL') || ''

            // Format service interests
            const serviceInterestFormatted =
              signup.serviceInterest
                ?.map((service) =>
                  service.replace('_', ' ').replace(/\b\w/g, (l) => l.toUpperCase())
                )
                .join(', ') || ''

            message
              .to(signup.emailAddress)
              .from(env.get('MAIL_FROM_ADDRESS'), env.get('MAIL_FROM_NAME'))
              .subject(
                'Great News! Your Salon Construction Service Application Has Been Approved 🎉'
              ).html(`
                <!DOCTYPE html>
                <html>
                  <head>
                  </head>
                  <body style="font-family: 'Inter', sans-serif; margin: 0; padding: 0; background: #f1f1f1;">
                    <div style="width: 950px; max-width: 100%; background: #f1f1f1; padding: 8px 24px; margin: 0 auto;">
                      <div style="text-align: center; margin-bottom: 10px;">
                        <img src="${serverDomain}/images/logo.png" style="width: 42px; height: 40px;" />
                      </div>
                      <div style="background: #ffffff; border-top: 4px solid #1d1c20; padding: 24px;">
                        <h1 style="font-size: 24px; font-weight: 600; color: #1d1c20; margin: 0 0 16px 0;">
                          Great News! Your Salon Construction Service Application Has Been Approved 🎉
                        </h1>

                        <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
                          Dear ${signup.fullName},
                        </p>

                        <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
                          Congratulations! We're excited to inform you that your salon construction service application has been approved.
                          We're thrilled to help bring your vision to life!
                        </p>

                        <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
                          <b>Your Application Details:</b>
                        </p>

                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 16px 0;">
                          <div style="font-size: 16px; font-weight: normal;">
                            <ul style="margin: 0; padding-left: 20px;">
                              <li><strong>Business Name:</strong> ${signup.businessName}</li>
                              <li><strong>Salon Address:</strong> ${signup.salonAddress}</li>
                              <li><strong>Contact Email:</strong> ${signup.emailAddress}</li>
                              <li><strong>Phone Number:</strong> ${signup.phoneNumber}</li>
                              ${signup.preferredStartDate ? `<li><strong>Preferred Start Date:</strong> ${signup.preferredStartDate.toFormat('MMM dd, yyyy')}</li>` : ''}
                              <li><strong>Budget Range:</strong> ${signup.budgetRange}</li>
                              ${serviceInterestFormatted ? `<li><strong>Services of Interest:</strong> ${serviceInterestFormatted}</li>` : ''}
                              ${signup.additionalNotes ? `<li><strong>Additional Notes:</strong> ${signup.additionalNotes}</li>` : ''}
                            </ul>
                          </div>
                        </div>

                        <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
                          <b>What happens next?</b>
                        </p>

                        <div style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
                          <ol>
                            <li>Our construction team will contact you within 2-3 business days to schedule an initial consultation</li>
                            <li>We'll conduct a detailed site assessment and discuss your specific requirements</li>
                            <li>You'll receive a comprehensive project proposal with timeline and pricing</li>
                            <li>Once approved, we'll begin transforming your salon space!</li>
                          </ol>
                        </div>

                        <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
                          We're committed to delivering exceptional results and creating a beautiful, functional space that will help your
                          business thrive. Our experienced team will work closely with you throughout the entire process.
                        </p>

                        <div style="background-color: #e3f2fd; padding: 16px; border-radius: 8px; margin: 20px 0;">
                          <p style="font-size: 16px; color: #1d1c20; line-height: 1.5; margin: 0;">
                            <strong>Questions or need immediate assistance?</strong><br>
                            📧 Email: <a href="mailto:${supportEmail}" style="color: #1976d2;">${supportEmail}</a><br>
                            📞 Phone: <a href="tel:${supportPhone}" style="color: #1976d2;">${supportPhone}</a>
                          </p>
                        </div>

                        <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
                          Thank you for choosing Zurno for your salon construction needs. We look forward to working with you!
                        </p>

                        <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
                          Best regards,<br>
                          <strong>The Zurno Construction Team</strong>
                        </p>
                      </div>

                      <div style="background: #ffffff; padding: 24px; color: #4f4d55; font-size: 12px; border-top: 1px solid #f1f1f1;">
                        <!-- Social icons aligned to the left with 16px margin spacing -->
                        <div style="display: flex; align-items: center; margin-bottom: 16px;">
                          <a target="_blank" href="https://www.facebook.com/NailsJobs/" style="margin-right: 16px;">
                            <img src="${serverDomain}/images/social-facebook.png" style="width: 28px; height: 28px;" alt="Facebook" />
                          </a>
                          <a target="_blank" href="https://www.instagram.com/zurnoinc/" style="margin-right: 16px;">
                            <img src="${serverDomain}/images/social-instagram.png" style="width: 28px; height: 28px;" alt="Instagram" />
                          </a>
                          <a target="_blank" href="https://www.tiktok.com/@zurnoinc" style="margin-right: 16px;">
                            <img src="${serverDomain}/images/social-tiktok.png" style="width: 28px; height: 28px;" alt="TikTok" />
                          </a>
                          <a target="_blank" href="https://www.youtube.com/channel/UC8jehfQ4KzHYoaTgSPLMCjw" style="margin-right: 16px;">
                            <img src="${serverDomain}/images/social-youtube.png" style="width: 28px; height: 28px;" alt="YouTube" />
                          </a>
                          <a target="_blank" href="https://zurno.com/">
                            <img src="${serverDomain}/images/website.png" style="width: 28px; height: 28px;" alt="Website" />
                          </a>
                        </div>

                        <p>
                          Need help? Visit our <a style="color: #1D1C20; text-decoration: underline;" href="https://zurno.com/">Help Center</a> or contact us at
                          <a target="_blank" style="color: #1D1C20; text-decoration: underline;" href="mailto:<EMAIL>"><EMAIL></a>.
                        </p>
                        <p>
                          Copyright © 2025 Zurno. All rights reserved.
                        </p>
                      </div>
                    </div>
                  </body>
                </html>
              `)
          })
        } else if (payload.status === ESalonConstructionServiceStatus.REJECTED) {
          console.log('Sending rejected email')

          await mail.sendLater((message) => {
            const supportEmail = env.get('SUPPORT_EMAIL') || ''
            const supportPhone = env.get('SUPPORT_PHONE_NUMBER') || ''
            const serverDomain = env.get('BASE_URL') || ''

            // Format service interests
            const serviceInterestFormatted =
              signup.serviceInterest
                ?.map((service) =>
                  service.replace('_', ' ').replace(/\b\w/g, (l) => l.toUpperCase())
                )
                .join(', ') || ''

            message
              .to(signup.emailAddress)
              .from(env.get('MAIL_FROM_ADDRESS'), env.get('MAIL_FROM_NAME'))
              .subject('Update on Your Salon Construction Service Application').html(`
                <!DOCTYPE html>
                <html>
                  <head>
                  </head>
                  <body style="font-family: 'Inter', sans-serif; margin: 0; padding: 0; background: #f1f1f1;">
                    <div style="width: 950px; max-width: 100%; background: #f1f1f1; padding: 8px 24px; margin: 0 auto;">
                      <div style="text-align: center; margin-bottom: 10px;">
                        <img src="${serverDomain}/images/logo.png" style="width: 42px; height: 40px;" />
                      </div>
                      <div style="background: #ffffff; border-top: 4px solid #1d1c20; padding: 24px;">
                        <h1 style="font-size: 24px; font-weight: 600; color: #1d1c20; margin: 0 0 16px 0;">
                          Update on Your Salon Construction Service Application
                        </h1>

                        <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
                          Dear ${signup.fullName},
                        </p>

                        <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
                          Thank you for your interest in our salon construction services. After careful review of your application,
                          we regret to inform you that we are unable to proceed with your project at this time.
                        </p>

                        <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
                          <b>Your Application Details:</b>
                        </p>

                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 16px 0;">
                          <div style="font-size: 16px; font-weight: normal;">
                            <ul style="margin: 0; padding-left: 20px;">
                              <li><strong>Business Name:</strong> ${signup.businessName}</li>
                              <li><strong>Salon Address:</strong> ${signup.salonAddress}</li>
                              <li><strong>Contact Email:</strong> ${signup.emailAddress}</li>
                              <li><strong>Phone Number:</strong> ${signup.phoneNumber}</li>
                              ${signup.preferredStartDate ? `<li><strong>Preferred Start Date:</strong> ${signup.preferredStartDate.toFormat('MMM dd, yyyy')}</li>` : ''}
                              <li><strong>Budget Range:</strong> ${signup.budgetRange}</li>
                              ${serviceInterestFormatted ? `<li><strong>Services of Interest:</strong> ${serviceInterestFormatted}</li>` : ''}
                            </ul>
                          </div>
                        </div>

                        ${
                          payload.rejectionReason
                            ? `
                        <div style="background-color: #fff3e0; padding: 16px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ff9800;">
                          <p style="font-size: 16px; color: #1d1c20; line-height: 1.5; margin: 0;">
                            <strong>Reason for Decision:</strong><br>
                            ${payload.rejectionReason}
                          </p>
                        </div>
                        `
                            : ''
                        }

                        <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
                          <b>What you can do next:</b>
                        </p>

                        <div style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
                          <ul>
                            <li>Review the feedback provided and consider addressing any specific concerns mentioned</li>
                            <li>You may reapply in the future if your circumstances change</li>
                            <li>Contact our team if you have questions about this decision or need clarification</li>
                            <li>Consider exploring alternative solutions that might better fit your current needs</li>
                          </ul>
                        </div>

                        <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
                          We appreciate the time you took to submit your application and your interest in working with Zurno.
                          While we cannot move forward with this particular project, we encourage you to stay in touch for future opportunities.
                        </p>

                        <div style="background-color: #e3f2fd; padding: 16px; border-radius: 8px; margin: 20px 0;">
                          <p style="font-size: 16px; color: #1d1c20; line-height: 1.5; margin: 0;">
                            <strong>Questions about this decision?</strong><br>
                            📧 Email: <a href="mailto:${supportEmail}" style="color: #1976d2;">${supportEmail}</a><br>
                            📞 Phone: <a href="tel:${supportPhone}" style="color: #1976d2;">${supportPhone}</a>
                          </p>
                        </div>

                        <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
                          Thank you for considering Zurno for your salon construction needs.
                        </p>

                        <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
                          Best regards,<br>
                          <strong>The Zurno Construction Team</strong>
                        </p>
                      </div>

                      <div style="background: #ffffff; padding: 24px; color: #4f4d55; font-size: 12px; border-top: 1px solid #f1f1f1;">
                        <!-- Social icons aligned to the left with 16px margin spacing -->
                        <div style="display: flex; align-items: center; margin-bottom: 16px;">
                          <a target="_blank" href="https://www.facebook.com/NailsJobs/" style="margin-right: 16px;">
                            <img src="${serverDomain}/images/social-facebook.png" style="width: 28px; height: 28px;" alt="Facebook" />
                          </a>
                          <a target="_blank" href="https://www.instagram.com/zurnoinc/" style="margin-right: 16px;">
                            <img src="${serverDomain}/images/social-instagram.png" style="width: 28px; height: 28px;" alt="Instagram" />
                          </a>
                          <a target="_blank" href="https://www.tiktok.com/@zurnoinc" style="margin-right: 16px;">
                            <img src="${serverDomain}/images/social-tiktok.png" style="width: 28px; height: 28px;" alt="TikTok" />
                          </a>
                          <a target="_blank" href="https://www.youtube.com/channel/UC8jehfQ4KzHYoaTgSPLMCjw" style="margin-right: 16px;">
                            <img src="${serverDomain}/images/social-youtube.png" style="width: 28px; height: 28px;" alt="YouTube" />
                          </a>
                          <a target="_blank" href="https://zurno.com/">
                            <img src="${serverDomain}/images/website.png" style="width: 28px; height: 28px;" alt="Website" />
                          </a>
                        </div>

                        <p>
                          Need help? Visit our <a style="color: #1D1C20; text-decoration: underline;" href="https://zurno.com/">Help Center</a> or contact us at
                          <a target="_blank" style="color: #1D1C20; text-decoration: underline;" href="mailto:<EMAIL>"><EMAIL></a>.
                        </p>
                        <p>
                          Copyright © 2025 Zurno. All rights reserved.
                        </p>
                      </div>
                    </div>
                  </body>
                </html>
              `)
          })
        }
      } catch (emailError) {
        console.error('Failed to send email notification:', emailError)
      }

      const message =
        payload.status === ESalonConstructionServiceStatus.APPROVED
          ? SALON_CONSTRUCTION_SERVICE_TEXT.APPROVAL_SUCCESS
          : SALON_CONSTRUCTION_SERVICE_TEXT.REJECTION_SUCCESS

      return response.ok({
        success: true,
        message,
        data: signup,
      })
    } catch (error) {
      console.error('Error updating salon construction service signup:', error)

      if (error.messages) {
        return response.badRequest({
          success: false,
          message: SALON_CONSTRUCTION_SERVICE_TEXT.VALIDATION_ERROR,
          errors: error.messages,
        })
      }

      return response.internalServerError({
        success: false,
        message: 'Failed to update signup',
      })
    }
  }

  /**
   * @destroy
   * @tag Admin Salon Construction Service
   * @summary Soft delete salon construction service signup
   * @paramPath id - Signup ID - @type(string) @required
   * @responseBody 200 - {"success":true,"message":"Signup deleted successfully"} - Success
   * @responseBody 404 - {"success":false,"message":"Salon construction service signup not found"} - Not Found
   */
  async destroy({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.SALON_CONSTRUCTION_SERVICE)

    try {
      const signup = await ZnSalonConstructionServiceSignup.findOrFail(params.id)
      await signup.softDelete()

      return response.ok({
        success: true,
        message: 'Signup deleted successfully',
      })
    } catch (error) {
      console.error('Error deleting salon construction service signup:', error)
      return response.internalServerError({
        success: false,
        message: 'Failed to delete signup',
      })
    }
  }

  /**
   * @downloadPdf
   * @tag Admin Salon Construction Service
   * @summary Download PDF for salon construction service signup
   * @paramPath id - Signup ID - @type(string) @required
   * @responseBody 200 - PDF file - Success
   * @responseBody 404 - {"success":false,"message":"PDF not found"} - Not Found
   */
  async downloadPdf({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.SALON_CONSTRUCTION_SERVICE)

    try {
      const signup = await ZnSalonConstructionServiceSignup.query()
        .where('id', params.id)
        .preload('pdfFile')
        .first()

      if (!signup || !signup.pdfFile) {
        return response.notFound({
          success: false,
          message: 'PDF not found',
        })
      }

      return response.redirect(signup.pdfFile.url)
    } catch (error) {
      console.error('Error downloading PDF:', error)
      return response.internalServerError({
        success: false,
        message: 'Failed to download PDF',
      })
    }
  }

  /**
   * @stats
   * @tag Admin Salon Construction Service
   * @summary Get salon construction service statistics
   * @responseBody 200 - {"success":true,"data":{"total":0,"pending":0,"approved":0,"rejected":0}} - Success
   */
  async stats({ bouncer, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.SALON_CONSTRUCTION_SERVICE)

    try {
      const [total, pending, approved, rejected] = await Promise.all([
        ZnSalonConstructionServiceSignup.query().count('* as total'),
        ZnSalonConstructionServiceSignup.query()
          .where('status', ESalonConstructionServiceStatus.PENDING)
          .count('* as total'),
        ZnSalonConstructionServiceSignup.query()
          .where('status', ESalonConstructionServiceStatus.APPROVED)
          .count('* as total'),
        ZnSalonConstructionServiceSignup.query()
          .where('status', ESalonConstructionServiceStatus.REJECTED)
          .count('* as total'),
      ])

      return response.ok({
        success: true,
        data: {
          total: total[0].$extras.total,
          pending: pending[0].$extras.total,
          approved: approved[0].$extras.total,
          rejected: rejected[0].$extras.total,
        },
      })
    } catch (error) {
      console.error('Error fetching salon construction service stats:', error)
      return response.internalServerError({
        success: false,
        message: 'Failed to fetch stats',
      })
    }
  }
}
