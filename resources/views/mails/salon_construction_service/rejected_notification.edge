@layout.home({serverDomain})
  @slot('main')
    <h1 style="font-size: 24px; font-weight: 600; color: #1d1c20; margin: 0 0 16px 0;">
      Update on Your Salon Construction Service Application
    </h1>

    <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
      Dear {{ signup.fullName }},
    </p>

    <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
      Thank you for your interest in our salon construction services. After careful review of your application, 
      we regret to inform you that we are unable to proceed with your project at this time.
    </p>

    <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
      <b>Your Application Details:</b>
    </p>

    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 16px 0;">
      <div style="font-size: 16px; font-weight: normal;">
        <ul style="margin: 0; padding-left: 20px;">
          <li><strong>Business Name:</strong> {{ signup.businessName }}</li>
          <li><strong>Salon Address:</strong> {{ signup.salonAddress }}</li>
          <li><strong>Contact Email:</strong> {{ signup.emailAddress }}</li>
          <li><strong>Phone Number:</strong> {{ signup.phoneNumber }}</li>
          
          @if(signup.preferredStartDate)
          <li><strong>Preferred Start Date:</strong> {{ signup.preferredStartDate.toFormat('MMM dd, yyyy') }}</li>
          @end
          
          <li><strong>Budget Range:</strong> {{ signup.budgetRange }}</li>
          
          @if(signup.serviceInterest && signup.serviceInterest.length > 0)
          <li><strong>Services of Interest:</strong> 
            @each(service in signup.serviceInterest)
              {{ service.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) }}{{ !$loop.last ? ', ' : '' }}
            @end
          </li>
          @end
        </ul>
      </div>
    </div>

    @if(rejectionReason)
    <div style="background-color: #fff3e0; padding: 16px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ff9800;">
      <p style="font-size: 16px; color: #1d1c20; line-height: 1.5; margin: 0;">
        <strong>Reason for Decision:</strong><br>
        {{ rejectionReason }}
      </p>
    </div>
    @end

    <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
      <b>What you can do next:</b>
    </p>

    <div style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
      <ul>
        <li>Review the feedback provided and consider addressing any specific concerns mentioned</li>
        <li>You may reapply in the future if your circumstances change</li>
        <li>Contact our team if you have questions about this decision or need clarification</li>
        <li>Consider exploring alternative solutions that might better fit your current needs</li>
      </ul>
    </div>

    <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
      We appreciate the time you took to submit your application and your interest in working with Zurno. 
      While we cannot move forward with this particular project, we encourage you to stay in touch for future opportunities.
    </p>

    <div style="background-color: #e3f2fd; padding: 16px; border-radius: 8px; margin: 20px 0;">
      <p style="font-size: 16px; color: #1d1c20; line-height: 1.5; margin: 0;">
        <strong>Questions about this decision?</strong><br>
        📧 Email: <a href="mailto:{{ supportEmail }}" style="color: #1976d2;">{{ supportEmail }}</a><br>
        📞 Phone: <a href="tel:{{ supportPhone }}" style="color: #1976d2;">{{ supportPhone }}</a>
      </p>
    </div>

    <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
      Thank you for considering Zurno for your salon construction needs.
    </p>

    <p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
      Best regards,<br>
      <strong>The Zurno Construction Team</strong>
    </p>
  @endslot
@end
